import { useState } from "react";
import { Modal, TextField, Button, Grid, FormControlLabel, Switch, MenuItem } from "@mui/material";
import ModalContainer from "../../../components/ModalContainer";
import S3ImageUpload from "../../../components/S3ImageUpload";
import { useToaster } from "../../../hooks/ToasterHook";
import theme from "../../../theme";
import { useApp } from "../../../hooks/AppHook";

const CreateVesselModal = ({
    open,
    onClose,
    onSubmit,
    units = [],
    unitsLoading = false,
    assignedUnitIds = [],
    assignedUnitIdsLoading = false,
    regions = [],
}) => {
    const toaster = useToaster();
    const { deviceHeight } = useApp();
    const [formData, setFormData] = useState({
        name: "",
        thumbnail_file: null,
        unit_id: "",
        is_active: true,
        region_group_id: "",
        home_port_location: "",
    });
    const [errors, setErrors] = useState({});
    const [loading, setLoading] = useState(false);

    const handleChange = (field) => (event) => {
        const value = field === "is_active" ? event.target.checked : event.target.value;
        setFormData((prev) => ({
            ...prev,
            [field]: value,
        }));

        if (errors[field]) {
            setErrors((prev) => ({
                ...prev,
                [field]: "",
            }));
        }
    };

    const validateForm = () => {
        const newErrors = {};

        if (!formData.name.trim()) {
            newErrors.name = "Vessel name is required";
        }
        if (!formData.region_group_id) {
            newErrors.region_group_id = "Region group is required";
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleSubmit = async () => {
        if (!validateForm()) {
            return;
        }

        setLoading(true);

        try {
            const submitData = new FormData();
            submitData.append("name", formData.name);
            if (formData.unit_id !== "" && formData.unit_id !== undefined) {
                submitData.append("unit_id", formData.unit_id);
            }
            submitData.append("is_active", formData.is_active);
            submitData.append("region_group_id", formData.region_group_id);

            if (formData.home_port_location && formData.home_port_location.trim()) {
                const coords = formData.home_port_location.split(",").map((coord) => parseFloat(coord.trim()));
                if (coords.length === 2 && !isNaN(coords[0]) && !isNaN(coords[1])) {
                    submitData.append("home_port_location", JSON.stringify([coords[1], coords[0]])); // Send the coordinates to the api as [ longitude, latitude]
                }
            }

            if (formData.thumbnail_file instanceof File) {
                submitData.append("thumbnail_file", formData.thumbnail_file);
            }

            const result = await onSubmit(submitData);

            if (result.success) {
                handleClose();
            } else {
                toaster(result.error || "Failed to create vessel", { variant: "error" });
            }
        } catch {
            toaster("An unexpected error occurred", { variant: "error" });
        } finally {
            setLoading(false);
        }
    };

    const handleClose = () => {
        setFormData({
            name: "",
            thumbnail_url: "",
            unit_id: "",
            is_active: true,
            region_group_id: "",
            home_port_location: "",
        });
        setErrors({});
        onClose();
    };

    return (
        <Modal open={open} onClose={handleClose}>
            <ModalContainer title={"Create New Vessel"} onClose={handleClose}>
                <Grid
                    container
                    direction="column"
                    sx={{ gap: 2, maxHeight: deviceHeight < 700 ? "60vh" : "80vh", overflow: "auto", flexWrap: "nowrap", flexDirection: "column" }}
                >
                    <Grid>
                        <TextField
                            value={formData.name}
                            sx={{ minWidth: { xs: 250, sm: 500 } }}
                            onChange={handleChange("name")}
                            label="Vessel Name"
                            variant="filled"
                            required
                            error={!!errors.name}
                        />
                    </Grid>

                    <Grid>
                        <TextField
                            select
                            value={formData.unit_id}
                            sx={{ minWidth: { xs: 250, sm: 500 } }}
                            onChange={handleChange("unit_id")}
                            label="Unit ID (Optional)"
                            variant="filled"
                            disabled={unitsLoading || assignedUnitIdsLoading}
                            error={!!errors.unit_id}
                            helperText={unitsLoading || assignedUnitIdsLoading ? "Loading units..." : errors.unit_id}
                        >
                            <MenuItem value={""}>
                                <em>No Unit Assigned</em>
                            </MenuItem>
                            {units.length === 0 && !unitsLoading ? (
                                <MenuItem disabled>No units available</MenuItem>
                            ) : (
                                units.map((unit) => {
                                    const isAssigned = assignedUnitIds.includes(unit.unit_id);

                                    return (
                                        <MenuItem key={unit.unit_id} value={unit.unit_id} disabled={isAssigned}>
                                            {unit.unit_id}
                                        </MenuItem>
                                    );
                                })
                            )}
                        </TextField>
                    </Grid>

                    <Grid>
                        <TextField
                            select
                            value={formData.region_group_id}
                            sx={{ minWidth: { xs: 250, sm: 500 } }}
                            onChange={handleChange("region_group_id")}
                            label="Select Region Group"
                            variant="filled"
                            error={!!errors.region_group_id}
                        >
                            {regions.length === 0 ? (
                                <MenuItem disabled>No regions available</MenuItem>
                            ) : (
                                regions.map((region) => (
                                    <MenuItem key={region._id} value={region._id}>
                                        {region.name} ({region.timezone})
                                    </MenuItem>
                                ))
                            )}
                        </TextField>
                    </Grid>

                    <Grid>
                        <TextField
                            type="text"
                            value={formData.home_port_location || ""}
                            onChange={handleChange("home_port_location")}
                            label="Home Port Location (latitude,longitude)"
                            variant="filled"
                            placeholder="e.g., 14.5995, 120.9842"
                            helperText="Enter coordinates as latitude, longitude (comma separated)"
                            error={!!errors.home_port_location}
                            fullWidth
                        />
                    </Grid>

                    <Grid>
                        <S3ImageUpload
                            value={formData.thumbnail_file}
                            onChange={(fileData) => setFormData((prev) => ({ ...prev, thumbnail_file: fileData }))}
                            label="Vessel Thumbnail (optional)"
                            maxSizeBytes={5 * 1024 * 1024} // 5MB
                            acceptedTypes="image/jpeg,image/jpg"
                            error={!!errors.thumbnail_file}
                            helperText={errors.thumbnail_file || "Upload a JPG or JPEG image for the vessel thumbnail"}
                        />
                    </Grid>

                    <Grid>
                        <FormControlLabel
                            control={
                                <Switch
                                    checked={formData.is_active}
                                    onChange={handleChange("is_active")}
                                    sx={{
                                        height: "50px",
                                        width: "80px",
                                        borderRadius: "50px",
                                        "& .MuiSwitch-switchBase": {
                                            padding: "15px 4px",
                                            transform: "translate(9px, -2px)",
                                        },
                                        "& .MuiSwitch-track": {
                                            backgroundColor: "#FFFFFF",
                                            height: "30px",
                                            borderRadius: "50px",
                                        },
                                        "& .Mui-checked+.MuiSwitch-track": {
                                            backgroundColor: theme.palette.custom.mainBlue + " !important",
                                            opacity: "1 !important",
                                        },
                                        "& .Mui-checked.MuiSwitch-switchBase": {
                                            transform: "translate(36px, -2px)",
                                        },
                                        "& .MuiSwitch-thumb": {
                                            backgroundColor: "#FFFFFF",
                                            height: "28px",
                                            width: "28px",
                                        },
                                        "& .Mui-disabled": {
                                            opacity: 0.4,
                                        },
                                        "& .Mui-disabled+.MuiSwitch-track": {
                                            opacity: "0.3 !important",
                                        },
                                    }}
                                />
                            }
                            label={formData.is_active ? "Active Vessel" : "Inactive Vessel"}
                        />
                    </Grid>
                </Grid>

                <Grid sx={{ justifyContent: "center", display: "flex", gap: 1 }}>
                    <Button
                        variant="contained"
                        onClick={handleSubmit}
                        disabled={loading || !formData.name || !!Object.keys(errors).find((key) => errors[key])}
                    >
                        {loading ? "Creating..." : "Create Vessel"}
                    </Button>
                </Grid>
            </ModalContainer>
        </Modal>
    );
};

export default CreateVesselModal;
