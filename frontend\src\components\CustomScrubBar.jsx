import { forwardRef, useCallback, useEffect, useRef, useState } from "react";
import { Box, Tooltip } from "@mui/material";
import theme from "../theme";

const CustomScrubBar = forwardRef(function CustomScrubBar(
    {
        value = 0,
        onChange,
        onChangeCommitted,
        onMouseDown: onMouseDownProp,
        marks = [],
        height = 24,
        sx = {},
        totalSeconds,
        getHoverTitle,
        tooltipContainer,
    },
    forwardedRef,
) {
    const rootRef = useRef(null);
    const trackRef = useRef(null);
    const thumbRef = useRef(null);
    const isDraggingRef = useRef(false);
    const [hoverSec, setHoverSec] = useState(null);

    const clamp = (v) => Math.max(0, Math.min(100, v));

    const applyPercent = (p) => {
        const percent = clamp(p);
        if (trackRef.current) trackRef.current.style.width = percent + "%";
        if (thumbRef.current) thumbRef.current.style.left = `calc(${percent}% - 4px)`;
    };

    const calcPercentFromEvent = useCallback((evt) => {
        const root = rootRef.current;
        if (!root) return 0;
        const rect = root.getBoundingClientRect();
        const clientX = evt.touches ? evt.touches[0].clientX : evt.clientX;
        return clamp(((clientX - rect.left) / rect.width) * 100);
    }, []);

    const handleStart = useCallback(
        (evt) => {
            if (onMouseDownProp) onMouseDownProp(evt);
            isDraggingRef.current = true;
            const percent = calcPercentFromEvent(evt);
            applyPercent(percent);
            onChange && onChange(evt, percent);
            evt.preventDefault?.();
        },
        [onMouseDownProp, onChange, calcPercentFromEvent],
    );

    const handleMove = useCallback(
        (evt) => {
            if (!isDraggingRef.current) return;
            const percent = calcPercentFromEvent(evt);
            applyPercent(percent);
            onChange && onChange(evt, percent);
            evt.preventDefault?.();
        },
        [onChange, calcPercentFromEvent],
    );

    const handleEnd = useCallback(
        (evt) => {
            if (!isDraggingRef.current) return;
            isDraggingRef.current = false;
            const refEvt = evt.changedTouches ? evt.changedTouches[0] : evt;
            const percent = calcPercentFromEvent(refEvt);
            applyPercent(percent);
            onChangeCommitted && onChangeCommitted(evt, percent);
            evt.preventDefault?.();
        },
        [onChangeCommitted, calcPercentFromEvent],
    );

    // Sync with external value when not dragging
    useEffect(() => {
        if (!isDraggingRef.current) applyPercent(value);
    }, [value]);

    useEffect(() => {
        const onMouseMove = (e) => handleMove(e);
        const onMouseUp = (e) => handleEnd(e);
        const onTouchMove = (e) => handleMove(e);
        const onTouchEnd = (e) => handleEnd(e);

        window.addEventListener("mousemove", onMouseMove);
        window.addEventListener("mouseup", onMouseUp);
        window.addEventListener("touchmove", onTouchMove, { passive: false });
        window.addEventListener("touchend", onTouchEnd, { passive: false });

        return () => {
            window.removeEventListener("mousemove", onMouseMove);
            window.removeEventListener("mouseup", onMouseUp);
            window.removeEventListener("touchmove", onTouchMove);
            window.removeEventListener("touchend", onTouchEnd);
        };
    }, [handleMove, handleEnd]);

    return (
        <Tooltip
            open={hoverSec !== null}
            title={getHoverTitle ? getHoverTitle(hoverSec || 0) : `${Math.round(hoverSec || 0)}s`}
            placement="top"
            arrow
            followCursor
            PopperProps={tooltipContainer ? { container: tooltipContainer } : undefined}
        >
            <Box
                ref={(node) => {
                    // Both forwardRef and local rootRef receive the DOM node
                    if (typeof forwardedRef === "function") forwardedRef(node);
                    else if (forwardedRef) forwardedRef.current = node;
                    rootRef.current = node;
                }}
                onMouseDown={handleStart}
                onTouchStart={handleStart}
                onMouseEnter={(e) => {
                    const p = calcPercentFromEvent(e);
                    setHoverSec((p / 100) * (totalSeconds || 100));
                }}
                onMouseMove={(e) => {
                    if (!isDraggingRef.current) {
                        const p = calcPercentFromEvent(e);
                        setHoverSec((p / 100) * (totalSeconds || 100));
                    }
                }}
                onMouseLeave={() => {
                    if (!isDraggingRef.current) setHoverSec(null);
                }}
                sx={{
                    position: "relative",
                    width: "100%",
                    height,
                    cursor: "pointer",
                    userSelect: "none",
                    "& .scrubber-rail": {
                        position: "absolute",
                        left: 0,
                        top: 0,
                        bottom: 0,
                        right: 0,
                        backgroundColor: "#FFFFFF4D",
                        opacity: 0.28,
                    },
                    "& .scrubber-track": {
                        position: "absolute",
                        left: 0,
                        top: 0,
                        bottom: 0,
                        width: `${clamp(value)}%`,
                        backgroundColor: theme.palette.custom.mainBlue,
                        willChange: "width",
                        transition: isDraggingRef.current ? "none" : "width 0.3s ease-out",
                    },
                    "& .scrubber-thumb": {
                        position: "absolute",
                        top: 0,
                        bottom: 0,
                        left: `calc(${clamp(value)}% - 4px)`,
                        width: 8,
                        backgroundColor: "#FFFFFF",
                        willChange: "left",
                        transition: isDraggingRef.current ? "none" : "left 0.3s ease-out",
                    },
                    "& .scrubber-mark": {
                        position: "absolute",
                        top: 0,
                        bottom: 0,
                        width: 4,
                        backgroundColor: "rgba(255,255,255,0.6)",
                    },
                    ...sx,
                }}
            >
                <Box className="scrubber-rail" />
                <Box className="scrubber-track" ref={trackRef} />
                {Array.isArray(marks) &&
                    marks?.length > 0 &&
                    marks.map((m, idx) => (
                        <Box key={`${idx}-${m.value}`} className="scrubber-mark" sx={{ left: `calc(${clamp(m.value)}% - 2px)` }} />
                    ))}
                <Box className="scrubber-thumb" ref={thumbRef} />
            </Box>
        </Tooltip>
    );
});

export default CustomScrubBar;
